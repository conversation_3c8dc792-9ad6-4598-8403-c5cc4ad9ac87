<template>
  <div class="responsive-layout h-screen flex flex-col">
    <!-- Title Section -->
    <header
      class="title-section bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between"
    >
      <div class="title-left">
        <h1 class="text-2xl font-bold text-gray-800">{{ title }}</h1>
        <p class="text-sm text-gray-600">{{ subtitle }}</p>
      </div>
      <div class="title-right flex items-center gap-2">
        <slot name="toolbar">
          <!-- Default toolbar content if no slot provided -->
        </slot>
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="content-area flex flex-1 overflow-hidden">
      <!-- Side List Section -->
      <aside
        :class="[
          'side-list-section bg-gray-50 border-r border-gray-200 transition-all duration-300 ease-in-out',
          'lg:w-80 lg:block',
          showSidebar ? 'w-full block' : 'w-0 hidden',
          'md:w-80 md:block',
        ]"
      >
        <div class="p-4">
          <div class="flex items-center justify-between mb-4 lg:hidden">
            <h2 class="text-lg font-semibold text-gray-800">Menu</h2>
            <Button
              icon="pi pi-times"
              severity="secondary"
              text
              rounded
              class="lg:hidden"
              @click="hideSidebar"
            />
          </div>

          <slot name="sidebar">
            <!-- Default sidebar content if no slot provided -->
            <div class="text-center py-8 text-gray-500">
              <p>No sidebar content provided</p>
            </div>
          </slot>
        </div>
      </aside>

      <!-- Main Section -->
      <main
        :class="[
          'main-section flex-1 bg-white overflow-auto transition-all duration-300 ease-in-out',
          !showSidebar || 'lg:block' ? 'block' : 'hidden lg:block',
        ]"
      >
        <div class="p-6">
          <!-- Mobile Menu Button -->
          <Button
            v-if="!showSidebar"
            icon="pi pi-bars"
            severity="secondary"
            text
            rounded
            class="lg:hidden mb-4"
            @click="showSidebar = true"
          />

          <!-- Main Content Slot -->
          <slot>
            <!-- Default content if no slot provided -->
            <div class="text-center py-12">
              <i class="pi pi-inbox text-6xl text-gray-300 mb-4"></i>
              <h3 class="text-xl font-semibold text-gray-600 mb-2">No content provided</h3>
              <p class="text-gray-500">Add content using the default slot</p>
            </div>
          </slot>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Button from 'primevue/button'

// Props
const props = defineProps({
  title: {
    type: String,
    default: 'Dashboard',
  },
  subtitle: {
    type: String,
    default: 'Manage your content and settings',
  },
})

// Reactive data
const showSidebar = ref(false)

// Methods
const hideSidebar = () => {
  showSidebar.value = false
}

// Initialize sidebar visibility
onMounted(() => {
  // Show sidebar by default on desktop
  if (window.innerWidth >= 1024) {
    showSidebar.value = true
  }
})
</script>

<style scoped>
.responsive-layout {
  font-family: 'Inter', sans-serif;
}

.menu-item {
  transition: all 0.2s ease-in-out;
}

.menu-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.content-wrapper {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar */
.main-section::-webkit-scrollbar {
  width: 6px;
}

.main-section::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
