<template>
  <div class="app-container">
    <HorizontalMenu v-if="$route.name !== 'login' && $route.name !== 'public'" />
    <RouterView />
    <Toast />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from './stores/auth'
import HorizontalMenu from './components/Navigation/HorizontalMenu.vue'

const authStore = useAuthStore()

// Initialize auth state on app mount
onMounted(() => {
  authStore.initializeAuth()
})
</script>

<style scoped></style>
