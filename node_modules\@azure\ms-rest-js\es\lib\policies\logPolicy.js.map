{"version": 3, "file": "logPolicy.js", "sourceRoot": "", "sources": ["../../../lib/policies/logPolicy.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,+FAA+F;;AAI/F,OAAO,EACL,iBAAiB,GAIlB,MAAM,iBAAiB,CAAC;AAEzB,MAAM,UAAU,SAAS,CAAC,MAAyB;IAAzB,uBAAA,EAAA,SAAc,OAAO,CAAC,GAAG;IACjD,OAAO;QACL,MAAM,EAAE,UAAC,UAAyB,EAAE,OAAiC;YACnE,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACpD,CAAC;KACF,CAAC;AACJ,CAAC;AAED;IAA+B,6BAAiB;IAG9C,mBACE,UAAyB,EACzB,OAAiC,EACjC,MAAyB;QAAzB,uBAAA,EAAA,SAAc,OAAO,CAAC,GAAG;QAH3B,YAKE,kBAAM,UAAU,EAAE,OAAO,CAAC,SAE3B;QADC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACvB,CAAC;IAEM,+BAAW,GAAlB,UAAmB,OAAwB;QAA3C,iBAEC;QADC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,WAAW,CAAC,KAAI,EAAE,QAAQ,CAAC,EAA3B,CAA2B,CAAC,CAAC;IAC/F,CAAC;IACH,gBAAC;AAAD,CAAC,AAfD,CAA+B,iBAAiB,GAe/C;;AAED,SAAS,WAAW,CAClB,MAAiB,EACjB,QAA+B;IAE/B,MAAM,CAAC,MAAM,CAAC,iBAAe,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAG,CAAC,CAAC;IAC/E,MAAM,CAAC,MAAM,CAAC,8BAA4B,QAAQ,CAAC,MAAQ,CAAC,CAAC;IAC7D,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC;IACzC,MAAM,CAAC,MAAM,CAAC,cAAY,YAAc,CAAC,CAAC;IAC1C,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC"}