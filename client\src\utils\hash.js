/**
 * Hash Utility Functions
 * Provides various hashing functions for string data
 */

/**
 * MD5 hash implementation
 * Based on the MD5 algorithm specification
 */

/**
 * Convert a string to MD5 hash
 * @param {string} str - The string to hash
 * @returns {string} - The MD5 hash as a hexadecimal string
 */
export function md5Hash(str) {
  // Convert string to UTF-8 bytes
  const utf8Bytes = new TextEncoder().encode(str)
  
  // MD5 implementation
  function md5(bytes) {
    // MD5 constants
    const s = [
      7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22,
      5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20,
      4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23,
      6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
    ]
    
    const K = [
      0xd76aa478, 0xe8c7b756, 0x242070db, 0xc1bd<PERSON>ee,
      0xf57c0faf, 0x4787c62a, 0xa8304613, 0xfd469501,
      0x698098d8, 0x8b44f7af, 0xffff5bb1, 0x895cd7be,
      0x6b901122, 0xfd987193, 0xa679438e, 0x49b40821,
      0xf61e2562, 0xc040b340, 0x265e5a51, 0xe9b6c7aa,
      0xd62f105d, 0x02441453, 0xd8a1e681, 0xe7d3fbc8,
      0x21e1cde6, 0xc33707d6, 0xf4d50d87, 0x455a14ed,
      0xa9e3e905, 0xfcefa3f8, 0x676f02d9, 0x8d2a4c8a,
      0xfffa3942, 0x8771f681, 0x6d9d6122, 0xfde5380c,
      0xa4beea44, 0x4bdecfa9, 0xf6bb4b60, 0xbebfbc70,
      0x289b7ec6, 0xeaa127fa, 0xd4ef3085, 0x04881d05,
      0xd9d4d039, 0xe6db99e5, 0x1fa27cf8, 0xc4ac5665,
      0xf4292244, 0x432aff97, 0xab9423a7, 0xfc93a039,
      0x655b59c3, 0x8f0ccc92, 0xffeff47d, 0x85845dd1,
      0x6fa87e4f, 0xfe2ce6e0, 0xa3014314, 0x4e0811a1,
      0xf7537e82, 0xbd3af235, 0x2ad7d2bb, 0xeb86d391
    ]
    
    // Initialize MD5 hash values
    let h0 = 0x67452301
    let h1 = 0xefcdab89
    let h2 = 0x98badcfe
    let h3 = 0x10325476
    
    // Pre-processing: adding padding bits
    const msgLen = bytes.length
    const paddedLen = Math.ceil((msgLen + 9) / 64) * 64
    const padded = new Uint8Array(paddedLen)
    padded.set(bytes)
    padded[msgLen] = 0x80
    
    // Append original length in bits as 64-bit little-endian integer
    const bitLen = msgLen * 8
    const view = new DataView(padded.buffer)
    view.setUint32(paddedLen - 8, bitLen, true)
    view.setUint32(paddedLen - 4, Math.floor(bitLen / 0x100000000), true)
    
    // Process the message in 512-bit chunks
    for (let chunk = 0; chunk < paddedLen; chunk += 64) {
      const w = new Uint32Array(16)
      for (let i = 0; i < 16; i++) {
        w[i] = view.getUint32(chunk + i * 4, true)
      }
      
      let a = h0, b = h1, c = h2, d = h3
      
      for (let i = 0; i < 64; i++) {
        let f, g
        if (i < 16) {
          f = (b & c) | (~b & d)
          g = i
        } else if (i < 32) {
          f = (d & b) | (~d & c)
          g = (5 * i + 1) % 16
        } else if (i < 48) {
          f = b ^ c ^ d
          g = (3 * i + 5) % 16
        } else {
          f = c ^ (b | ~d)
          g = (7 * i) % 16
        }
        
        f = (f + a + K[i] + w[g]) >>> 0
        a = d
        d = c
        c = b
        b = (b + leftRotate(f, s[i])) >>> 0
      }
      
      h0 = (h0 + a) >>> 0
      h1 = (h1 + b) >>> 0
      h2 = (h2 + c) >>> 0
      h3 = (h3 + d) >>> 0
    }
    
    // Produce the final hash value as a 128-bit number (little-endian)
    const result = new ArrayBuffer(16)
    const resultView = new DataView(result)
    resultView.setUint32(0, h0, true)
    resultView.setUint32(4, h1, true)
    resultView.setUint32(8, h2, true)
    resultView.setUint32(12, h3, true)
    
    return new Uint8Array(result)
  }
  
  // Helper function for left rotation
  function leftRotate(value, amount) {
    return (value << amount) | (value >>> (32 - amount))
  }
  
  // Generate MD5 hash
  const hashBytes = md5(utf8Bytes)
  
  // Convert to hexadecimal string
  return Array.from(hashBytes)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('')
}

/**
 * Simple hash function (alternative to MD5 for non-cryptographic use)
 * @param {string} str - The string to hash
 * @returns {string} - A simple hash as hexadecimal string
 */
export function simpleHash(str) {
  let hash = 0
  if (str.length === 0) return hash.toString(16)
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(16)
}

/**
 * Generate a random hash-like string
 * @param {number} length - Length of the hash string (default: 32)
 * @returns {string} - Random hexadecimal string
 */
export function randomHash(length = 32) {
  const chars = '0123456789abcdef'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars[Math.floor(Math.random() * chars.length)]
  }
  return result
}
