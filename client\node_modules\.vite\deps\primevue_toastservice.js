import {
  PrimeVueToastSymbol
} from "./chunk-ZSQGNQS2.js";
import {
  ToastEventBus
} from "./chunk-QABQFO3C.js";
import "./chunk-O5TNY3RC.js";
import "./chunk-J4DVLWB4.js";
import "./chunk-BUSYA2B4.js";

// node_modules/primevue/toastservice/index.mjs
var ToastService = {
  install: function install(app) {
    var ToastService2 = {
      add: function add(message) {
        ToastEventBus.emit("add", message);
      },
      remove: function remove(message) {
        ToastEventBus.emit("remove", message);
      },
      removeGroup: function removeGroup(group) {
        ToastEventBus.emit("remove-group", group);
      },
      removeAllGroups: function removeAllGroups() {
        ToastEventBus.emit("remove-all-groups");
      }
    };
    app.config.globalProperties.$toast = ToastService2;
    app.provide(PrimeVueToastSymbol, ToastService2);
  }
};
export {
  ToastService as default
};
//# sourceMappingURL=primevue_toastservice.js.map
