/**
 * API Usage Examples
 * This file demonstrates how to use the reusable API client functions
 */

import { apiGet, apiPost, apiPut, apiDelete, ApiError } from './api'

/**
 * Example: Fetch user profile
 */
export async function getUserProfile(userId) {
  try {
    const response = await apiGet(`/user/${userId}`)
    return response
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('API Error:', error.message, error.status)
    }
    throw error
  }
}

/**
 * Example: Update user profile
 */
export async function updateUserProfile(userId, profileData) {
  try {
    const response = await apiPut(`/user/${userId}`, profileData)
    return response
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('API Error:', error.message, error.status)
    }
    throw error
  }
}

/**
 * Example: Fetch data with query parameters
 */
export async function fetchDataWithParams(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = `/data${queryString ? `?${queryString}` : ''}`
    const response = await apiGet(endpoint)
    return response
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('API Error:', error.message, error.status)
    }
    throw error
  }
}

/**
 * Example: Upload file (multipart/form-data)
 */
export async function uploadFile(file, additionalData = {}) {
  try {
    const formData = new FormData()
    formData.append('file', file)
    
    // Add additional data to form
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key])
    })

    const response = await apiPost('/upload', formData, {
      headers: {
        // Don't set Content-Type, let browser set it with boundary
        'Content-Type': undefined,
      },
    })
    return response
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Upload Error:', error.message, error.status)
    }
    throw error
  }
}

/**
 * Example: Delete resource
 */
export async function deleteResource(resourceId) {
  try {
    const response = await apiDelete(`/resource/${resourceId}`)
    return response
  } catch (error) {
    if (error instanceof ApiError) {
      console.error('Delete Error:', error.message, error.status)
    }
    throw error
  }
}
