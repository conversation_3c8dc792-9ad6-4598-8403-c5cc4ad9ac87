import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { loginApi, logoutApi, setAuthToken } from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const isAuthenticated = ref(false)

  // Getters
  const currentUser = computed(() => user.value)
  const isLoggedIn = computed(() => isAuthenticated.value)

  // Actions
  const login = async (username, password) => {
    try {
      const response = await loginApi(username, password)

      if (response.success && response.data && response.data.length > 0) {
        const userData = response.data[0]
        user.value = userData
        isAuthenticated.value = true

        // Store in localStorage for persistence
        localStorage.setItem('user', JSON.stringify(userData))
        localStorage.setItem('isAuthenticated', 'true')

        return { success: true, data: userData }
      } else {
        throw new Error(response.message || 'Lo<PERSON> failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.message || '<PERSON><PERSON> failed. Please try again.'
      }
    }
  }

  const logout = async () => {
    try {
      await logoutApi()
      user.value = null
      isAuthenticated.value = false

      // Clear localStorage
      localStorage.removeItem('user')
      localStorage.removeItem('isAuthenticated')
      localStorage.removeItem('authToken')

      // Note: Router navigation should be handled by the component calling this function
      return { success: true }
    } catch (error) {
      console.error('Logout error:', error)
      // Still clear local state even if API call fails
      user.value = null
      isAuthenticated.value = false
      localStorage.removeItem('user')
      localStorage.removeItem('isAuthenticated')
      localStorage.removeItem('authToken')

      return { success: false, message: error.message }
    }
  }

  const initializeAuth = () => {
    // Check if user is already logged in (from localStorage)
    const storedUser = localStorage.getItem('user')
    const storedAuth = localStorage.getItem('isAuthenticated')
    const storedToken = localStorage.getItem('authToken')

    if (storedUser && storedAuth === 'true' && storedToken) {
      user.value = JSON.parse(storedUser)
      isAuthenticated.value = true
      setAuthToken(storedToken) // Ensure token is available for API calls
    }
  }

  const updatePassword = async (currentPassword, newPassword) => {
    // This would typically make an API call to update the password
    // For now, we'll just simulate the process
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Simulate API call
        if (currentPassword === 'wrongpassword') {
          reject(new Error('Current password is incorrect'))
        } else {
          // Update user data if needed
          resolve({ success: true, message: 'Password updated successfully' })
        }
      }, 1000)
    })
  }

  return {
    // State
    user,
    isAuthenticated,
    // Getters
    currentUser,
    isLoggedIn,
    // Actions
    login,
    logout,
    initializeAuth,
    updatePassword
  }
})
