<template>
  <div class="card">
    <Menubar :model="menuItems">
      <template #start>
        <svg
          width="35"
          height="40"
          viewBox="0 0 35 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="h-8"
        >
          <path
            d="M25.87 18.05L23.16 17.45L25.27 20.46V29.78L32.49 23.76V13.53L29.18 14.73L25.87 18.04V18.05ZM25.27 35.49L29.18 31.58V27.67L25.27 30.98V35.49ZM20.16 17.14H20.03H20.17H20.16ZM30.1 5.19L34.89 4.81L33.08 12.33L24.1 15.67L30.08 5.2L30.1 5.19ZM5.72 14.74L2.41 13.54V23.77L9.63 29.79V20.47L11.74 17.46L9.03 18.06L5.72 14.75V14.74ZM9.63 30.98L5.72 27.67V31.58L9.63 35.49V30.98ZM4.8 5.2L10.78 15.67L1.81 12.33L0 4.81L4.79 5.19L4.8 5.2ZM24.37 21.05V34.59L22.56 37.29L20.46 39.4H14.44L12.34 37.29L10.53 34.59V21.05L12.42 18.23L17.45 26.8L22.48 18.23L24.37 21.05ZM22.85 0L22.57 0.69L17.45 13.08L12.33 0.69L12.05 0H22.85Z"
            fill="var(--p-primary-color)"
          />
          <path
            d="M30.69 4.21L24.37 4.81L22.57 0.69L22.86 0H26.48L30.69 4.21ZM23.75 5.67L22.66 3.08L18.05 14.24V17.14H19.7H20.03H20.16H20.2L24.1 15.7L30.11 5.19L23.75 5.67ZM4.21002 4.21L10.53 4.81L12.33 0.69L12.05 0H8.43002L4.22002 4.21H4.21002ZM21.9 17.4L20.6 18.2H14.3L13 17.4L12.4 18.2L12.42 18.23L17.45 26.8L22.48 18.23L22.5 18.2L21.9 17.4ZM4.79002 5.19L10.8 15.7L14.7 17.14H14.74H15.2H16.85V14.24L12.24 3.09L11.15 5.68L4.79002 5.2V5.19Z"
            fill="var(--p-text-color)"
          />
        </svg>
      </template>
      <template #item="{ item, props, hasSubmenu, root }">
        <a v-ripple class="flex items-center" v-bind="props.action">
          <span>{{ item.label }}</span>
          <Badge v-if="item.badge" :value="item.badge" />
          <span
            v-if="item.shortcut"
            class="ml-auto border border-surface rounded bg-emphasis text-muted-color text-xs p-1"
          >
            {{ item.shortcut }}
          </span>
          <i
            v-if="hasSubmenu"
            :class="[
              'pi pi-angle-down ml-auto',
              { 'pi-angle-down': root, 'pi-angle-right': !root },
            ]"
          />
        </a>
      </template>
      <template #end>
        <div class="flex items-center gap-2 p-2 rounded-lg">
          <InputText placeholder="Search" type="text" class="w-32 sm:w-auto" />
          <Avatar
            image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png"
            shape="circle"
            class="cursor-pointer hover:ring-2 hover:ring-blue-300 transition-all duration-200"
            @click="toggleUserMenu"
          />

          <!-- User Menu Overlay -->
          <Popover ref="userMenuRef" class="user-menu-overlay">
            <div class="user-menu-content">
              <!-- User Info -->
              <div class="user-info">
                <Avatar
                  image="https://primefaces.org/cdn/primevue/images/avatar/amyelsner.png"
                  shape="circle"
                  size="large"
                />
                <div class="user-details">
                  <h4 class="user-name">{{ currentUser?.name || 'User' }}</h4>
                  <p class="user-email">{{ currentUser?.email || '<EMAIL>' }}</p>
                </div>
              </div>

              <!-- Menu Items -->
              <div class="menu-items">
                <div class="menu-item" @click="goToChangePassword">
                  <i class="pi pi-key"></i>
                  <span>Ubah Password</span>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-item logout-item" @click="handleLogout">
                  <i class="pi pi-sign-out"></i>
                  <span>Logout</span>
                </div>
              </div>
            </div>
          </Popover>
        </div>
      </template>
    </Menubar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMenuStore } from '@/stores/menu'
import Menubar from 'primevue/menubar'
import Avatar from 'primevue/avatar'
import InputText from 'primevue/inputtext'
import Badge from 'primevue/badge'
import Popover from 'primevue/popover'

const router = useRouter()
const authStore = useAuthStore()
const menuStore = useMenuStore()

// Make router available globally for menu navigation
window.router = router

// Refs
const userMenuRef = ref()

// Computed
const currentUser = computed(() => authStore.currentUser)
const menuItems = computed(() => {
  const transformedItems = menuStore.transformedMenuItems

  // Update command functions to use the current router instance
  const updateCommands = (items) => {
    return items.map((item) => {
      if (item.url) {
        item.command = () => {
          router.push(item.url)
        }
      }
      if (item.items && item.items.length > 0) {
        item.items = updateCommands(item.items)
      }
      return item
    })
  }

  return updateCommands([...transformedItems])
})

// Methods
const toggleUserMenu = (event) => {
  userMenuRef.value.toggle(event)
}

const goToChangePassword = () => {
  userMenuRef.value.hide()
  router.push('/change-password')
}

const handleLogout = () => {
  userMenuRef.value.hide()
  authStore.logout()
  router.push('/login')
}

// Initialize menu on component mount
onMounted(() => {
  if (authStore.isLoggedIn && !menuStore.isLoaded) {
    menuStore.loadMenu()
  }
})
</script>

<style scoped>
/* User Menu Styles */
:deep(.user-menu-overlay) {
  min-width: 280px;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.user-menu-content {
  padding: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  border-radius: 12px 12px 0 0;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.user-email {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.menu-items {
  padding: 8px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #495057;
}

.menu-item:hover {
  background-color: #f8f9fa;
  color: #333;
}

.menu-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.logout-item {
  color: #dc3545;
}

.logout-item:hover {
  background-color: #f8d7da;
  color: #721c24;
}

.menu-divider {
  height: 1px;
  background-color: #dee2e6;
  margin: 8px 0;
}

/* Avatar hover effect */
.cursor-pointer:hover {
  transform: scale(1.05);
}
</style>
