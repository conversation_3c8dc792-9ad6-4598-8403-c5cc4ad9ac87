{"version": 3, "sources": ["../../src/focustrap/style/FocusTrapStyle.js", "../../src/focustrap/BaseFocusTrap.js", "../../src/focustrap/FocusTrap.js", "../../src/popover/style/PopoverStyle.js", "../../src/popover/BasePopover.vue", "../../src/popover/Popover.vue", "../../src/popover/Popover.vue", "../../src/overlaypanel/OverlayPanel.vue"], "sourcesContent": ["import BaseStyle from '@primevue/core/base/style';\n\nexport default BaseStyle.extend({\n    name: 'focustrap-directive'\n});\n", "import BaseDirective from '@primevue/core/basedirective';\nimport FocusTrapStyle from 'primevue/focustrap/style';\n\nconst BaseFocusTrap = BaseDirective.extend({\n    style: FocusTrapStyle\n});\n\nexport default BaseFocusTrap;\n", "import { createElement, focus, getFirstFocusableElement, getLastFocusableElement, isFocusableElement } from '@primeuix/utils/dom';\nimport { isNotEmpty } from '@primeuix/utils/object';\nimport BaseFocusTrap from './BaseFocusTrap';\n\nconst FocusTrap = BaseFocusTrap.extend('focustrap', {\n    mounted(el, binding) {\n        const { disabled } = binding.value || {};\n\n        if (!disabled) {\n            this.createHiddenFocusableElements(el, binding);\n            this.bind(el, binding);\n            this.autoElementFocus(el, binding);\n        }\n\n        el.setAttribute('data-pd-focustrap', true);\n\n        this.$el = el;\n    },\n    updated(el, binding) {\n        const { disabled } = binding.value || {};\n\n        disabled && this.unbind(el);\n    },\n    unmounted(el) {\n        this.unbind(el);\n    },\n    methods: {\n        getComputedSelector(selector) {\n            return `:not(.p-hidden-focusable):not([data-p-hidden-focusable=\"true\"])${selector ?? ''}`;\n        },\n        bind(el, binding) {\n            const { onFocusIn, onFocusOut } = binding.value || {};\n\n            el.$_pfocustrap_mutationobserver = new MutationObserver((mutationList) => {\n                mutationList.forEach((mutation) => {\n                    if (mutation.type === 'childList' && !el.contains(document.activeElement)) {\n                        const findNextFocusableElement = (_el) => {\n                            const focusableElement = isFocusableElement(_el)\n                                ? isFocusableElement(_el, this.getComputedSelector(el.$_pfocustrap_focusableselector))\n                                    ? _el\n                                    : getFirstFocusableElement(el, this.getComputedSelector(el.$_pfocustrap_focusableselector))\n                                : getFirstFocusableElement(_el);\n\n                            return isNotEmpty(focusableElement) ? focusableElement : _el.nextSibling && findNextFocusableElement(_el.nextSibling);\n                        };\n\n                        focus(findNextFocusableElement(mutation.nextSibling));\n                    }\n                });\n            });\n\n            el.$_pfocustrap_mutationobserver.disconnect();\n            el.$_pfocustrap_mutationobserver.observe(el, {\n                childList: true\n            });\n\n            el.$_pfocustrap_focusinlistener = (event) => onFocusIn && onFocusIn(event);\n            el.$_pfocustrap_focusoutlistener = (event) => onFocusOut && onFocusOut(event);\n\n            el.addEventListener('focusin', el.$_pfocustrap_focusinlistener);\n            el.addEventListener('focusout', el.$_pfocustrap_focusoutlistener);\n        },\n        unbind(el) {\n            el.$_pfocustrap_mutationobserver && el.$_pfocustrap_mutationobserver.disconnect();\n            el.$_pfocustrap_focusinlistener && el.removeEventListener('focusin', el.$_pfocustrap_focusinlistener) && (el.$_pfocustrap_focusinlistener = null);\n            el.$_pfocustrap_focusoutlistener && el.removeEventListener('focusout', el.$_pfocustrap_focusoutlistener) && (el.$_pfocustrap_focusoutlistener = null);\n        },\n        autoFocus(options) {\n            this.autoElementFocus(this.$el, { value: { ...options, autoFocus: true } });\n        },\n        autoElementFocus(el, binding) {\n            const { autoFocusSelector = '', firstFocusableSelector = '', autoFocus = false } = binding.value || {};\n            let focusableElement = getFirstFocusableElement(el, `[autofocus]${this.getComputedSelector(autoFocusSelector)}`);\n\n            autoFocus && !focusableElement && (focusableElement = getFirstFocusableElement(el, this.getComputedSelector(firstFocusableSelector)));\n            focus(focusableElement);\n        },\n        onFirstHiddenElementFocus(event) {\n            const { currentTarget, relatedTarget } = event;\n            const focusableElement =\n                relatedTarget === currentTarget.$_pfocustrap_lasthiddenfocusableelement || !this.$el?.contains(relatedTarget)\n                    ? getFirstFocusableElement(currentTarget.parentElement, this.getComputedSelector(currentTarget.$_pfocustrap_focusableselector))\n                    : currentTarget.$_pfocustrap_lasthiddenfocusableelement;\n\n            focus(focusableElement);\n        },\n        onLastHiddenElementFocus(event) {\n            const { currentTarget, relatedTarget } = event;\n            const focusableElement =\n                relatedTarget === currentTarget.$_pfocustrap_firsthiddenfocusableelement || !this.$el?.contains(relatedTarget)\n                    ? getLastFocusableElement(currentTarget.parentElement, this.getComputedSelector(currentTarget.$_pfocustrap_focusableselector))\n                    : currentTarget.$_pfocustrap_firsthiddenfocusableelement;\n\n            focus(focusableElement);\n        },\n        createHiddenFocusableElements(el, binding) {\n            const { tabIndex = 0, firstFocusableSelector = '', lastFocusableSelector = '' } = binding.value || {};\n\n            const createFocusableElement = (onFocus) => {\n                return createElement('span', {\n                    class: 'p-hidden-accessible p-hidden-focusable',\n                    tabIndex,\n                    role: 'presentation',\n                    'aria-hidden': true,\n                    'data-p-hidden-accessible': true,\n                    'data-p-hidden-focusable': true,\n                    onFocus: onFocus?.bind(this)\n                });\n            };\n\n            const firstFocusableElement = createFocusableElement(this.onFirstHiddenElementFocus);\n            const lastFocusableElement = createFocusableElement(this.onLastHiddenElementFocus);\n\n            firstFocusableElement.$_pfocustrap_lasthiddenfocusableelement = lastFocusableElement;\n            firstFocusableElement.$_pfocustrap_focusableselector = firstFocusableSelector;\n            firstFocusableElement.setAttribute('data-pc-section', 'firstfocusableelement');\n\n            lastFocusableElement.$_pfocustrap_firsthiddenfocusableelement = firstFocusableElement;\n            lastFocusableElement.$_pfocustrap_focusableselector = lastFocusableSelector;\n            lastFocusableElement.setAttribute('data-pc-section', 'lastfocusableelement');\n\n            el.prepend(firstFocusableElement);\n            el.append(lastFocusableElement);\n        }\n    }\n});\n\nexport default FocusTrap;\n", "import { style } from '@primeuix/styles/popover';\nimport BaseStyle from '@primevue/core/base/style';\n\nconst classes = {\n    root: 'p-popover p-component',\n    content: 'p-popover-content'\n};\n\nexport default BaseStyle.extend({\n    name: 'popover',\n    style,\n    classes\n});\n", "<script>\nimport BaseComponent from '@primevue/core/basecomponent';\nimport PopoverStyle from 'primevue/popover/style';\n\nexport default {\n    name: 'BasePopover',\n    extends: BaseComponent,\n    props: {\n        dismissable: {\n            type: Boolean,\n            default: true\n        },\n        appendTo: {\n            type: [String, Object],\n            default: 'body'\n        },\n        baseZIndex: {\n            type: Number,\n            default: 0\n        },\n        autoZIndex: {\n            type: Boolean,\n            default: true\n        },\n        breakpoints: {\n            type: Object,\n            default: null\n        },\n        closeOnEscape: {\n            type: Boolean,\n            default: true\n        }\n    },\n    style: PopoverStyle,\n    provide() {\n        return {\n            $pcPopover: this,\n            $parentInstance: this\n        };\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\">\n        <transition name=\"p-popover\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"visible\" :ref=\"containerRef\" v-focustrap role=\"dialog\" :aria-modal=\"visible\" @click=\"onOverlayClick\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n                <slot v-if=\"$slots.container\" name=\"container\" :closeCallback=\"hide\" :keydownCallback=\"(event) => onButtonKeydown(event)\"></slot>\n                <template v-else>\n                    <div :class=\"cx('content')\" @click=\"onContentClick\" @mousedown=\"onContentClick\" @keydown=\"onContentKeydown\" v-bind=\"ptm('content')\">\n                        <slot></slot>\n                    </div>\n                </template>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { $dt } from '@primeuix/styled';\nimport { absolutePosition, addClass, addStyle, focus, getOffset, isClient, isTouchDevice, setAttribute } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport FocusTrap from 'primevue/focustrap';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport BasePopover from './BasePopover.vue';\n\nexport default {\n    name: 'Popover',\n    extends: BasePopover,\n    inheritAttrs: false,\n    emits: ['show', 'hide'],\n    data() {\n        return {\n            visible: false\n        };\n    },\n    watch: {\n        dismissable: {\n            immediate: true,\n            handler(newValue) {\n                if (newValue) {\n                    this.bindOutsideClickListener();\n                } else {\n                    this.unbindOutsideClickListener();\n                }\n            }\n        }\n    },\n    selfClick: false,\n    target: null,\n    eventTarget: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    styleElement: null,\n    overlayEventListener: null,\n    documentKeydownListener: null,\n    beforeUnmount() {\n        if (this.dismissable) {\n            this.unbindOutsideClickListener();\n        }\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.destroyStyle();\n        this.unbindResizeListener();\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        if (this.overlayEventListener) {\n            OverlayEventBus.off('overlay-click', this.overlayEventListener);\n            this.overlayEventListener = null;\n        }\n\n        this.container = null;\n    },\n    mounted() {\n        if (this.breakpoints) {\n            this.createStyle();\n        }\n    },\n    methods: {\n        toggle(event, target) {\n            if (this.visible) this.hide();\n            else this.show(event, target);\n        },\n        show(event, target) {\n            this.visible = true;\n            this.eventTarget = event.currentTarget;\n            this.target = target || event.currentTarget;\n        },\n        hide() {\n            this.visible = false;\n        },\n        onContentClick() {\n            this.selfClick = true;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n\n            if (this.dismissable) {\n                this.bindOutsideClickListener();\n            }\n\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('overlay', el, this.baseZIndex + this.$primevue.config.zIndex.overlay);\n            }\n\n            this.overlayEventListener = (e) => {\n                if (this.container.contains(e.target)) {\n                    this.selfClick = true;\n                }\n            };\n\n            this.focus();\n            OverlayEventBus.on('overlay-click', this.overlayEventListener);\n            this.$emit('show');\n\n            if (this.closeOnEscape) {\n                this.bindDocumentKeyDownListener();\n            }\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n            this.unbindDocumentKeyDownListener();\n            OverlayEventBus.off('overlay-click', this.overlayEventListener);\n            this.overlayEventListener = null;\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target, false);\n\n            const containerOffset = getOffset(this.container);\n            const targetOffset = getOffset(this.target);\n            let arrowLeft = 0;\n\n            if (containerOffset.left < targetOffset.left) {\n                arrowLeft = targetOffset.left - containerOffset.left;\n            }\n\n            this.container.style.setProperty($dt('popover.arrow.left').name, `${arrowLeft}px`);\n\n            if (containerOffset.top < targetOffset.top) {\n                this.container.setAttribute('data-p-popover-flipped', 'true');\n                !this.isUnstyled && addClass(this.container, 'p-popover-flipped');\n            }\n        },\n        onContentKeydown(event) {\n            if (event.code === 'Escape' && this.closeOnEscape) {\n                this.hide();\n                focus(this.target);\n            }\n        },\n        onButtonKeydown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                case 'ArrowUp':\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    event.preventDefault();\n\n                default:\n                    break;\n            }\n        },\n        focus() {\n            let focusTarget = this.container.querySelector('[autofocus]');\n\n            if (focusTarget) {\n                focusTarget.focus();\n            }\n        },\n        onKeyDown(event) {\n            if (event.code === 'Escape' && this.closeOnEscape) {\n                this.visible = false;\n            }\n        },\n        bindDocumentKeyDownListener() {\n            if (!this.documentKeydownListener) {\n                this.documentKeydownListener = this.onKeyDown.bind(this);\n                window.document.addEventListener('keydown', this.documentKeydownListener);\n            }\n        },\n        unbindDocumentKeyDownListener() {\n            if (this.documentKeydownListener) {\n                window.document.removeEventListener('keydown', this.documentKeydownListener);\n                this.documentKeydownListener = null;\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener && isClient()) {\n                this.outsideClickListener = (event) => {\n                    if (this.visible && !this.selfClick && !this.isTargetClicked(event)) {\n                        this.visible = false;\n                    }\n\n                    this.selfClick = false;\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n                this.selfClick = false;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.visible) {\n                        this.visible = false;\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.visible && !isTouchDevice()) {\n                        this.visible = false;\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        isTargetClicked(event) {\n            return this.eventTarget && (this.eventTarget === event.target || this.eventTarget.contains(event.target));\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        createStyle() {\n            if (!this.styleElement && !this.isUnstyled) {\n                this.styleElement = document.createElement('style');\n                this.styleElement.type = 'text/css';\n                setAttribute(this.styleElement, 'nonce', this.$primevue?.config?.csp?.nonce);\n                document.head.appendChild(this.styleElement);\n\n                let innerHTML = '';\n\n                for (let breakpoint in this.breakpoints) {\n                    innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-popover[${this.$attrSelector}] {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n                }\n\n                this.styleElement.innerHTML = innerHTML;\n            }\n        },\n        destroyStyle() {\n            if (this.styleElement) {\n                document.head.removeChild(this.styleElement);\n                this.styleElement = null;\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        }\n    },\n    directives: {\n        focustrap: FocusTrap,\n        ripple: Ripple\n    },\n    components: {\n        Portal\n    }\n};\n</script>\n", "<template>\n    <Portal :appendTo=\"appendTo\">\n        <transition name=\"p-popover\" @enter=\"onEnter\" @leave=\"onLeave\" @after-leave=\"onAfterLeave\" v-bind=\"ptm('transition')\">\n            <div v-if=\"visible\" :ref=\"containerRef\" v-focustrap role=\"dialog\" :aria-modal=\"visible\" @click=\"onOverlayClick\" :class=\"cx('root')\" v-bind=\"ptmi('root')\">\n                <slot v-if=\"$slots.container\" name=\"container\" :closeCallback=\"hide\" :keydownCallback=\"(event) => onButtonKeydown(event)\"></slot>\n                <template v-else>\n                    <div :class=\"cx('content')\" @click=\"onContentClick\" @mousedown=\"onContentClick\" @keydown=\"onContentKeydown\" v-bind=\"ptm('content')\">\n                        <slot></slot>\n                    </div>\n                </template>\n            </div>\n        </transition>\n    </Portal>\n</template>\n\n<script>\nimport { $dt } from '@primeuix/styled';\nimport { absolutePosition, addClass, addStyle, focus, getOffset, isClient, isTouchDevice, setAttribute } from '@primeuix/utils/dom';\nimport { ZIndex } from '@primeuix/utils/zindex';\nimport { ConnectedOverlayScrollHandler } from '@primevue/core/utils';\nimport FocusTrap from 'primevue/focustrap';\nimport OverlayEventBus from 'primevue/overlayeventbus';\nimport Portal from 'primevue/portal';\nimport Ripple from 'primevue/ripple';\nimport BasePopover from './BasePopover.vue';\n\nexport default {\n    name: 'Popover',\n    extends: BasePopover,\n    inheritAttrs: false,\n    emits: ['show', 'hide'],\n    data() {\n        return {\n            visible: false\n        };\n    },\n    watch: {\n        dismissable: {\n            immediate: true,\n            handler(newValue) {\n                if (newValue) {\n                    this.bindOutsideClickListener();\n                } else {\n                    this.unbindOutsideClickListener();\n                }\n            }\n        }\n    },\n    selfClick: false,\n    target: null,\n    eventTarget: null,\n    outsideClickListener: null,\n    scrollHandler: null,\n    resizeListener: null,\n    container: null,\n    styleElement: null,\n    overlayEventListener: null,\n    documentKeydownListener: null,\n    beforeUnmount() {\n        if (this.dismissable) {\n            this.unbindOutsideClickListener();\n        }\n\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n\n        this.destroyStyle();\n        this.unbindResizeListener();\n        this.target = null;\n\n        if (this.container && this.autoZIndex) {\n            ZIndex.clear(this.container);\n        }\n\n        if (this.overlayEventListener) {\n            OverlayEventBus.off('overlay-click', this.overlayEventListener);\n            this.overlayEventListener = null;\n        }\n\n        this.container = null;\n    },\n    mounted() {\n        if (this.breakpoints) {\n            this.createStyle();\n        }\n    },\n    methods: {\n        toggle(event, target) {\n            if (this.visible) this.hide();\n            else this.show(event, target);\n        },\n        show(event, target) {\n            this.visible = true;\n            this.eventTarget = event.currentTarget;\n            this.target = target || event.currentTarget;\n        },\n        hide() {\n            this.visible = false;\n        },\n        onContentClick() {\n            this.selfClick = true;\n        },\n        onEnter(el) {\n            addStyle(el, { position: 'absolute', top: '0' });\n            this.alignOverlay();\n\n            if (this.dismissable) {\n                this.bindOutsideClickListener();\n            }\n\n            this.bindScrollListener();\n            this.bindResizeListener();\n\n            if (this.autoZIndex) {\n                ZIndex.set('overlay', el, this.baseZIndex + this.$primevue.config.zIndex.overlay);\n            }\n\n            this.overlayEventListener = (e) => {\n                if (this.container.contains(e.target)) {\n                    this.selfClick = true;\n                }\n            };\n\n            this.focus();\n            OverlayEventBus.on('overlay-click', this.overlayEventListener);\n            this.$emit('show');\n\n            if (this.closeOnEscape) {\n                this.bindDocumentKeyDownListener();\n            }\n        },\n        onLeave() {\n            this.unbindOutsideClickListener();\n            this.unbindScrollListener();\n            this.unbindResizeListener();\n            this.unbindDocumentKeyDownListener();\n            OverlayEventBus.off('overlay-click', this.overlayEventListener);\n            this.overlayEventListener = null;\n            this.$emit('hide');\n        },\n        onAfterLeave(el) {\n            if (this.autoZIndex) {\n                ZIndex.clear(el);\n            }\n        },\n        alignOverlay() {\n            absolutePosition(this.container, this.target, false);\n\n            const containerOffset = getOffset(this.container);\n            const targetOffset = getOffset(this.target);\n            let arrowLeft = 0;\n\n            if (containerOffset.left < targetOffset.left) {\n                arrowLeft = targetOffset.left - containerOffset.left;\n            }\n\n            this.container.style.setProperty($dt('popover.arrow.left').name, `${arrowLeft}px`);\n\n            if (containerOffset.top < targetOffset.top) {\n                this.container.setAttribute('data-p-popover-flipped', 'true');\n                !this.isUnstyled && addClass(this.container, 'p-popover-flipped');\n            }\n        },\n        onContentKeydown(event) {\n            if (event.code === 'Escape' && this.closeOnEscape) {\n                this.hide();\n                focus(this.target);\n            }\n        },\n        onButtonKeydown(event) {\n            switch (event.code) {\n                case 'ArrowDown':\n                case 'ArrowUp':\n                case 'ArrowLeft':\n                case 'ArrowRight':\n                    event.preventDefault();\n\n                default:\n                    break;\n            }\n        },\n        focus() {\n            let focusTarget = this.container.querySelector('[autofocus]');\n\n            if (focusTarget) {\n                focusTarget.focus();\n            }\n        },\n        onKeyDown(event) {\n            if (event.code === 'Escape' && this.closeOnEscape) {\n                this.visible = false;\n            }\n        },\n        bindDocumentKeyDownListener() {\n            if (!this.documentKeydownListener) {\n                this.documentKeydownListener = this.onKeyDown.bind(this);\n                window.document.addEventListener('keydown', this.documentKeydownListener);\n            }\n        },\n        unbindDocumentKeyDownListener() {\n            if (this.documentKeydownListener) {\n                window.document.removeEventListener('keydown', this.documentKeydownListener);\n                this.documentKeydownListener = null;\n            }\n        },\n        bindOutsideClickListener() {\n            if (!this.outsideClickListener && isClient()) {\n                this.outsideClickListener = (event) => {\n                    if (this.visible && !this.selfClick && !this.isTargetClicked(event)) {\n                        this.visible = false;\n                    }\n\n                    this.selfClick = false;\n                };\n\n                document.addEventListener('click', this.outsideClickListener);\n            }\n        },\n        unbindOutsideClickListener() {\n            if (this.outsideClickListener) {\n                document.removeEventListener('click', this.outsideClickListener);\n                this.outsideClickListener = null;\n                this.selfClick = false;\n            }\n        },\n        bindScrollListener() {\n            if (!this.scrollHandler) {\n                this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                    if (this.visible) {\n                        this.visible = false;\n                    }\n                });\n            }\n\n            this.scrollHandler.bindScrollListener();\n        },\n        unbindScrollListener() {\n            if (this.scrollHandler) {\n                this.scrollHandler.unbindScrollListener();\n            }\n        },\n        bindResizeListener() {\n            if (!this.resizeListener) {\n                this.resizeListener = () => {\n                    if (this.visible && !isTouchDevice()) {\n                        this.visible = false;\n                    }\n                };\n\n                window.addEventListener('resize', this.resizeListener);\n            }\n        },\n        unbindResizeListener() {\n            if (this.resizeListener) {\n                window.removeEventListener('resize', this.resizeListener);\n                this.resizeListener = null;\n            }\n        },\n        isTargetClicked(event) {\n            return this.eventTarget && (this.eventTarget === event.target || this.eventTarget.contains(event.target));\n        },\n        containerRef(el) {\n            this.container = el;\n        },\n        createStyle() {\n            if (!this.styleElement && !this.isUnstyled) {\n                this.styleElement = document.createElement('style');\n                this.styleElement.type = 'text/css';\n                setAttribute(this.styleElement, 'nonce', this.$primevue?.config?.csp?.nonce);\n                document.head.appendChild(this.styleElement);\n\n                let innerHTML = '';\n\n                for (let breakpoint in this.breakpoints) {\n                    innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-popover[${this.$attrSelector}] {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n                }\n\n                this.styleElement.innerHTML = innerHTML;\n            }\n        },\n        destroyStyle() {\n            if (this.styleElement) {\n                document.head.removeChild(this.styleElement);\n                this.styleElement = null;\n            }\n        },\n        onOverlayClick(event) {\n            OverlayEventBus.emit('overlay-click', {\n                originalEvent: event,\n                target: this.target\n            });\n        }\n    },\n    directives: {\n        focustrap: FocusTrap,\n        ripple: Ripple\n    },\n    components: {\n        Portal\n    }\n};\n</script>\n", "<script>\nimport Popover from 'primevue/popover';\n\nexport default {\n    name: 'OverlayPanel',\n    extends: Popover,\n    mounted() {\n        console.warn('Deprecated since v4. Use Popover component instead.');\n    }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAA,iBAAeA,UAAUC,OAAO;EAC5BC,MAAM;AACV,CAAC;;;ACDD,IAAMC,gBAAgBC,cAAcC,OAAO;EACvCC,OAAOC;AACX,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACDD,IAAMC,YAAYL,cAAcE,OAAO,aAAa;EAChDI,SAAO,SAAPA,QAAQC,IAAIC,SAAS;AACjB,QAAAC,OAAqBD,QAAQE,SAAS,CAAA,GAA9BC,WAAQF,KAARE;AAER,QAAI,CAACA,UAAU;AACX,WAAKC,8BAA8BL,IAAIC,OAAO;AAC9C,WAAKK,KAAKN,IAAIC,OAAO;AACrB,WAAKM,iBAAiBP,IAAIC,OAAO;IACrC;AAEAD,OAAGQ,aAAa,qBAAqB,IAAI;AAEzC,SAAKC,MAAMT;;EAEfU,SAAO,SAAPA,QAAQV,IAAIC,SAAS;AACjB,QAAAU,QAAqBV,QAAQE,SAAS,CAAA,GAA9BC,WAAQO,MAARP;AAERA,gBAAY,KAAKQ,OAAOZ,EAAE;;EAE9Ba,WAAAA,SAAAA,UAAUb,IAAI;AACV,SAAKY,OAAOZ,EAAE;;EAElBc,SAAS;IACLC,qBAAAA,SAAAA,oBAAoBC,UAAU;AAC1B,aAAAC,kEAAAA,OAAyED,aAAQ,QAARA,aAAAA,SAAAA,WAAY,EAAE;;IAE3FV,MAAI,SAAJA,KAAKN,IAAIC,SAAS;AAAA,UAAAiB,QAAA;AACd,UAAAC,QAAkClB,QAAQE,SAAS,CAAA,GAA3CiB,YAASD,MAATC,WAAWC,aAAUF,MAAVE;AAEnBrB,SAAGsB,gCAAgC,IAAIC,iBAAiB,SAACC,cAAiB;AACtEA,qBAAaC,QAAQ,SAACC,UAAa;AAC/B,cAAIA,SAASC,SAAS,eAAe,CAAC3B,GAAG4B,SAASC,SAASC,aAAa,GAAG;AACvE,gBAAMC,4BAA2B,SAA3BA,yBAA4BC,KAAQ;AACtC,kBAAMC,mBAAmBC,mBAAmBF,GAAG,IACzCE,mBAAmBF,KAAKd,MAAKH,oBAAoBf,GAAGmC,8BAA8B,CAAC,IAC/EH,MACAI,yBAAyBpC,IAAIkB,MAAKH,oBAAoBf,GAAGmC,8BAA8B,CAAC,IAC5FC,yBAAyBJ,GAAG;AAElC,qBAAOK,WAAWJ,gBAAgB,IAAIA,mBAAmBD,IAAIM,eAAeP,0BAAyBC,IAAIM,WAAW;;AAGxHC,kBAAMR,0BAAyBL,SAASY,WAAW,CAAC;UACxD;QACJ,CAAC;MACL,CAAC;AAEDtC,SAAGsB,8BAA8BkB,WAAU;AAC3CxC,SAAGsB,8BAA8BmB,QAAQzC,IAAI;QACzC0C,WAAW;MACf,CAAC;AAED1C,SAAG2C,+BAA+B,SAACC,OAAK;AAAA,eAAKxB,aAAaA,UAAUwB,KAAK;MAAC;AAC1E5C,SAAG6C,gCAAgC,SAACD,OAAK;AAAA,eAAKvB,cAAcA,WAAWuB,KAAK;MAAC;AAE7E5C,SAAG8C,iBAAiB,WAAW9C,GAAG2C,4BAA4B;AAC9D3C,SAAG8C,iBAAiB,YAAY9C,GAAG6C,6BAA6B;;IAEpEjC,QAAAA,SAAAA,OAAOZ,IAAI;AACPA,SAAGsB,iCAAiCtB,GAAGsB,8BAA8BkB,WAAU;AAC/ExC,SAAG2C,gCAAgC3C,GAAG+C,oBAAoB,WAAW/C,GAAG2C,4BAA4B,MAAM3C,GAAG2C,+BAA+B;AAC5I3C,SAAG6C,iCAAiC7C,GAAG+C,oBAAoB,YAAY/C,GAAG6C,6BAA6B,MAAM7C,GAAG6C,gCAAgC;;IAEpJG,WAAAA,SAAAA,UAAUC,SAAS;AACf,WAAK1C,iBAAiB,KAAKE,KAAK;QAAEN,OAAK+C,cAAAA,cAAA,CAAA,GAAOD,OAAO,GAAA,CAAA,GAAA;UAAED,WAAW;QAAI,CAAA;MAAG,CAAC;;IAE9EzC,kBAAgB,SAAhBA,iBAAiBP,IAAIC,SAAS;AAC1B,UAAAkD,QAAmFlD,QAAQE,SAAS,CAAA,GAAEiD,wBAAAD,MAA9FE,mBAAAA,oBAAiBD,0BAAG,SAAA,KAAEA,uBAAAE,wBAAAH,MAAEI,wBAAAA,yBAAsBD,0BAAG,SAAA,KAAEA,uBAAAE,kBAAAL,MAAEH,WAAAA,aAASQ,oBAAG,SAAA,QAAKA;AAC9E,UAAIvB,mBAAmBG,yBAAyBpC,IAAEiB,cAAAA,OAAgB,KAAKF,oBAAoBsC,iBAAiB,CAAC,CAAE;AAE/GL,MAAAA,cAAa,CAACf,qBAAqBA,mBAAmBG,yBAAyBpC,IAAI,KAAKe,oBAAoBwC,sBAAsB,CAAC;AACnIhB,YAAMN,gBAAgB;;IAE1BwB,2BAAAA,SAAAA,0BAA0Bb,OAAO;AAAA,UAAAc;AAC7B,UAAQC,gBAAiCf,MAAjCe,eAAeC,gBAAkBhB,MAAlBgB;AACvB,UAAM3B,mBACF2B,kBAAkBD,cAAcE,2CAA2C,GAAAH,YAAC,KAAKjD,SAAG,QAAAiD,cAARA,UAAAA,UAAU9B,SAASgC,aAAa,KACtGxB,yBAAyBuB,cAAcG,eAAe,KAAK/C,oBAAoB4C,cAAcxB,8BAA8B,CAAC,IAC5HwB,cAAcE;AAExBtB,YAAMN,gBAAgB;;IAE1B8B,0BAAAA,SAAAA,yBAAyBnB,OAAO;AAAA,UAAAoB;AAC5B,UAAQL,gBAAiCf,MAAjCe,eAAeC,gBAAkBhB,MAAlBgB;AACvB,UAAM3B,mBACF2B,kBAAkBD,cAAcM,4CAA4C,GAAAD,aAAC,KAAKvD,SAAG,QAAAuD,eAARA,UAAAA,WAAUpC,SAASgC,aAAa,KACvGM,wBAAwBP,cAAcG,eAAe,KAAK/C,oBAAoB4C,cAAcxB,8BAA8B,CAAC,IAC3HwB,cAAcM;AAExB1B,YAAMN,gBAAgB;;IAE1B5B,+BAA6B,SAA7BA,8BAA8BL,IAAIC,SAAS;AAAA,UAAAkE,SAAA;AACvC,UAAAC,QAAkFnE,QAAQE,SAAS,CAAA,GAAEkE,iBAAAD,MAA7FE,UAAAA,WAAQD,mBAAG,SAAA,IAACA,gBAAAE,wBAAAH,MAAEb,wBAAAA,yBAAsBgB,0BAAG,SAAA,KAAEA,uBAAAC,wBAAAJ,MAAEK,uBAAAA,wBAAqBD,0BAAG,SAAA,KAAEA;AAE7E,UAAME,yBAAyB,SAAzBA,wBAA0BC,SAAY;AACxC,eAAOC,cAAc,QAAQ;UACzB,SAAO;UACPN;UACAO,MAAM;UACN,eAAe;UACf,4BAA4B;UAC5B,2BAA2B;UAC3BF,SAASA,YAAAA,QAAAA,YAAO,SAAA,SAAPA,QAASrE,KAAK6D,MAAI;QAC/B,CAAC;;AAGL,UAAMW,wBAAwBJ,uBAAuB,KAAKjB,yBAAyB;AACnF,UAAMsB,uBAAuBL,uBAAuB,KAAKX,wBAAwB;AAEjFe,4BAAsBjB,0CAA0CkB;AAChED,4BAAsB3C,iCAAiCoB;AACvDuB,4BAAsBtE,aAAa,mBAAmB,uBAAuB;AAE7EuE,2BAAqBd,2CAA2Ca;AAChEC,2BAAqB5C,iCAAiCsC;AACtDM,2BAAqBvE,aAAa,mBAAmB,sBAAsB;AAE3ER,SAAGgF,QAAQF,qBAAqB;AAChC9E,SAAGiF,OAAOF,oBAAoB;IAClC;EACJ;AACJ,CAAC;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1HD,IAAMG,UAAU;EACZC,MAAM;EACNC,SAAS;AACb;AAEA,IAAA,eAAeC,UAAUC,OAAO;EAC5BC,MAAM;EACNC;EACAN;AACJ,CAAC;;;ACRD,IAAA,WAAe;EACXO,MAAM;EACN,WAASC;EACTC,OAAO;IACHC,aAAa;MACTC,MAAMC;MACN,WAAS;;IAEbC,UAAU;MACNF,MAAM,CAACG,QAAQC,MAAM;MACrB,WAAS;;IAEbC,YAAY;MACRL,MAAMM;MACN,WAAS;;IAEbC,YAAY;MACRP,MAAMC;MACN,WAAS;;IAEbO,aAAa;MACTR,MAAMI;MACN,WAAS;;IAEbK,eAAe;MACXT,MAAMC;MACN,WAAS;IACb;;EAEJS,OAAOC;EACPC,SAAO,SAAPA,UAAU;AACN,WAAO;MACHC,YAAY;MACZC,iBAAiB;;EAEzB;AACJ;ACdA,IAAAC,UAAe;EACXnB,MAAM;EACN,WAASoB;EACTC,cAAc;EACdC,OAAO,CAAC,QAAQ,MAAM;EACtBC,MAAI,SAAJA,OAAO;AACH,WAAO;MACHC,SAAS;;;EAGjBC,OAAO;IACHtB,aAAa;MACTuB,WAAW;MACXC,SAAAA,SAAAA,QAAQC,UAAU;AACd,YAAIA,UAAU;AACV,eAAKC,yBAAwB;QACjC,OAAO;AACH,eAAKC,2BAA0B;QACnC;MACJ;IACJ;;EAEJC,WAAW;EACXC,QAAQ;EACRC,aAAa;EACbC,sBAAsB;EACtBC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,sBAAsB;EACtBC,yBAAyB;EACzBC,eAAa,SAAbA,gBAAgB;AACZ,QAAI,KAAKtC,aAAa;AAClB,WAAK2B,2BAA0B;IACnC;AAEA,QAAI,KAAKK,eAAe;AACpB,WAAKA,cAAcO,QAAO;AAC1B,WAAKP,gBAAgB;IACzB;AAEA,SAAKQ,aAAY;AACjB,SAAKC,qBAAoB;AACzB,SAAKZ,SAAS;AAEd,QAAI,KAAKK,aAAa,KAAK1B,YAAY;AACnCkC,aAAOC,MAAM,KAAKT,SAAS;IAC/B;AAEA,QAAI,KAAKE,sBAAsB;AAC3BQ,sBAAgBC,IAAI,iBAAiB,KAAKT,oBAAoB;AAC9D,WAAKA,uBAAuB;IAChC;AAEA,SAAKF,YAAY;;EAErBY,SAAO,SAAPA,WAAU;AACN,QAAI,KAAKrC,aAAa;AAClB,WAAKsC,YAAW;IACpB;;EAEJC,SAAS;IACLC,QAAM,SAANA,OAAOC,OAAOrB,QAAQ;AAClB,UAAI,KAAKR,QAAS,MAAK8B,KAAI;UACtB,MAAKC,KAAKF,OAAOrB,MAAM;;IAEhCuB,MAAI,SAAJA,KAAKF,OAAOrB,QAAQ;AAChB,WAAKR,UAAU;AACf,WAAKS,cAAcoB,MAAMG;AACzB,WAAKxB,SAASA,UAAUqB,MAAMG;;IAElCF,MAAI,SAAJA,OAAO;AACH,WAAK9B,UAAU;;IAEnBiC,gBAAc,SAAdA,iBAAiB;AACb,WAAK1B,YAAY;;IAErB2B,SAAAA,SAAAA,QAAQC,IAAI;AAAA,UAAAC,QAAA;AACRC,eAASF,IAAI;QAAEG,UAAU;QAAYC,KAAK;MAAI,CAAC;AAC/C,WAAKC,aAAY;AAEjB,UAAI,KAAK7D,aAAa;AAClB,aAAK0B,yBAAwB;MACjC;AAEA,WAAKoC,mBAAkB;AACvB,WAAKC,mBAAkB;AAEvB,UAAI,KAAKvD,YAAY;AACjBkC,eAAOsB,IAAI,WAAWR,IAAI,KAAKlD,aAAa,KAAK2D,UAAUC,OAAOC,OAAOC,OAAO;MACpF;AAEA,WAAKhC,uBAAuB,SAACiC,GAAM;AAC/B,YAAIZ,MAAKvB,UAAUoC,SAASD,EAAExC,MAAM,GAAG;AACnC4B,gBAAK7B,YAAY;QACrB;;AAGJ,WAAK2C,MAAK;AACV3B,sBAAgB4B,GAAG,iBAAiB,KAAKpC,oBAAoB;AAC7D,WAAKqC,MAAM,MAAM;AAEjB,UAAI,KAAK/D,eAAe;AACpB,aAAKgE,4BAA2B;MACpC;;IAEJC,SAAO,SAAPA,UAAU;AACN,WAAKhD,2BAA0B;AAC/B,WAAKiD,qBAAoB;AACzB,WAAKnC,qBAAoB;AACzB,WAAKoC,8BAA6B;AAClCjC,sBAAgBC,IAAI,iBAAiB,KAAKT,oBAAoB;AAC9D,WAAKA,uBAAuB;AAC5B,WAAKqC,MAAM,MAAM;;IAErBK,cAAAA,SAAAA,aAAatB,IAAI;AACb,UAAI,KAAKhD,YAAY;AACjBkC,eAAOC,MAAMa,EAAE;MACnB;;IAEJK,cAAY,SAAZA,eAAe;AACXkB,uBAAiB,KAAK7C,WAAW,KAAKL,QAAQ,KAAK;AAEnD,UAAMmD,kBAAkBC,UAAU,KAAK/C,SAAS;AAChD,UAAMgD,eAAeD,UAAU,KAAKpD,MAAM;AAC1C,UAAIsD,YAAY;AAEhB,UAAIH,gBAAgBI,OAAOF,aAAaE,MAAM;AAC1CD,oBAAYD,aAAaE,OAAOJ,gBAAgBI;MACpD;AAEA,WAAKlD,UAAUvB,MAAM0E,YAAYC,IAAI,oBAAoB,EAAEzF,MAAI,GAAA0F,OAAKJ,WAAS,IAAA,CAAI;AAEjF,UAAIH,gBAAgBpB,MAAMsB,aAAatB,KAAK;AACxC,aAAK1B,UAAUsD,aAAa,0BAA0B,MAAM;AAC5D,SAAC,KAAKC,cAAcC,SAAS,KAAKxD,WAAW,mBAAmB;MACpE;;IAEJyD,kBAAAA,SAAAA,iBAAiBzC,OAAO;AACpB,UAAIA,MAAM0C,SAAS,YAAY,KAAKlF,eAAe;AAC/C,aAAKyC,KAAI;AACToB,cAAM,KAAK1C,MAAM;MACrB;;IAEJgE,iBAAAA,SAAAA,gBAAgB3C,OAAO;AACnB,cAAQA,MAAM0C,MAAI;QACd,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACD1C,gBAAM4C,eAAc;MAI5B;;IAEJvB,OAAK,SAALA,SAAQ;AACJ,UAAIwB,cAAc,KAAK7D,UAAU8D,cAAc,aAAa;AAE5D,UAAID,aAAa;AACbA,oBAAYxB,MAAK;MACrB;;IAEJ0B,WAAAA,SAAAA,UAAU/C,OAAO;AACb,UAAIA,MAAM0C,SAAS,YAAY,KAAKlF,eAAe;AAC/C,aAAKW,UAAU;MACnB;;IAEJqD,6BAA2B,SAA3BA,8BAA8B;AAC1B,UAAI,CAAC,KAAKrC,yBAAyB;AAC/B,aAAKA,0BAA0B,KAAK4D,UAAUC,KAAK,IAAI;AACvDC,eAAOC,SAASC,iBAAiB,WAAW,KAAKhE,uBAAuB;MAC5E;;IAEJwC,+BAA6B,SAA7BA,gCAAgC;AAC5B,UAAI,KAAKxC,yBAAyB;AAC9B8D,eAAOC,SAASE,oBAAoB,WAAW,KAAKjE,uBAAuB;AAC3E,aAAKA,0BAA0B;MACnC;;IAEJX,0BAAwB,SAAxBA,2BAA2B;AAAA,UAAA6E,SAAA;AACvB,UAAI,CAAC,KAAKxE,wBAAwByE,SAAQ,GAAI;AAC1C,aAAKzE,uBAAuB,SAACmB,OAAU;AACnC,cAAIqD,OAAKlF,WAAW,CAACkF,OAAK3E,aAAa,CAAC2E,OAAKE,gBAAgBvD,KAAK,GAAG;AACjEqD,mBAAKlF,UAAU;UACnB;AAEAkF,iBAAK3E,YAAY;;AAGrBwE,iBAASC,iBAAiB,SAAS,KAAKtE,oBAAoB;MAChE;;IAEJJ,4BAA0B,SAA1BA,6BAA6B;AACzB,UAAI,KAAKI,sBAAsB;AAC3BqE,iBAASE,oBAAoB,SAAS,KAAKvE,oBAAoB;AAC/D,aAAKA,uBAAuB;AAC5B,aAAKH,YAAY;MACrB;;IAEJkC,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAA4C,SAAA;AACjB,UAAI,CAAC,KAAK1E,eAAe;AACrB,aAAKA,gBAAgB,IAAI2E,8BAA8B,KAAK9E,QAAQ,WAAM;AACtE,cAAI6E,OAAKrF,SAAS;AACdqF,mBAAKrF,UAAU;UACnB;QACJ,CAAC;MACL;AAEA,WAAKW,cAAc8B,mBAAkB;;IAEzCc,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAK5C,eAAe;AACpB,aAAKA,cAAc4C,qBAAoB;MAC3C;;IAEJb,oBAAkB,SAAlBA,qBAAqB;AAAA,UAAA6C,SAAA;AACjB,UAAI,CAAC,KAAK3E,gBAAgB;AACtB,aAAKA,iBAAiB,WAAM;AACxB,cAAI2E,OAAKvF,WAAW,CAACwF,cAAa,GAAI;AAClCD,mBAAKvF,UAAU;UACnB;;AAGJ8E,eAAOE,iBAAiB,UAAU,KAAKpE,cAAc;MACzD;;IAEJQ,sBAAoB,SAApBA,uBAAuB;AACnB,UAAI,KAAKR,gBAAgB;AACrBkE,eAAOG,oBAAoB,UAAU,KAAKrE,cAAc;AACxD,aAAKA,iBAAiB;MAC1B;;IAEJwE,iBAAAA,SAAAA,gBAAgBvD,OAAO;AACnB,aAAO,KAAKpB,gBAAgB,KAAKA,gBAAgBoB,MAAMrB,UAAU,KAAKC,YAAYwC,SAASpB,MAAMrB,MAAM;;IAE3GiF,cAAAA,SAAAA,aAAatD,IAAI;AACb,WAAKtB,YAAYsB;;IAErBT,aAAW,SAAXA,cAAc;AACV,UAAI,CAAC,KAAKZ,gBAAgB,CAAC,KAAKsD,YAAY;AAAA,YAAAsB;AACxC,aAAK5E,eAAeiE,SAASY,cAAc,OAAO;AAClD,aAAK7E,aAAalC,OAAO;AACzBuF,qBAAa,KAAKrD,cAAc,UAAO4E,kBAAE,KAAK9C,eAAS8C,QAAAA,oBAAA,WAAAA,kBAAdA,gBAAgB7C,YAAM,QAAA6C,oBAAA,WAAAA,kBAAtBA,gBAAwBE,SAAG,QAAAF,oBAAA,SAAA,SAA3BA,gBAA6BG,KAAK;AAC3Ed,iBAASe,KAAKC,YAAY,KAAKjF,YAAY;AAE3C,YAAIkF,YAAY;AAEhB,iBAASC,cAAc,KAAK7G,aAAa;AACrC4G,uBAAU,2DAAA9B,OAC0B+B,YAAU/B,8CAAAA,EAAAA,OACzB,KAAKgC,eAAahC,8CAAAA,EAAAA,OAClB,KAAK9E,YAAY6G,UAAU,GAG/C,8FAAA;QACL;AAEA,aAAKnF,aAAakF,YAAYA;MAClC;;IAEJ7E,cAAY,SAAZA,eAAe;AACX,UAAI,KAAKL,cAAc;AACnBiE,iBAASe,KAAKK,YAAY,KAAKrF,YAAY;AAC3C,aAAKA,eAAe;MACxB;;IAEJsF,gBAAAA,SAAAA,eAAevE,OAAO;AAClBN,sBAAgB8E,KAAK,iBAAiB;QAClCC,eAAezE;QACfrB,QAAQ,KAAKA;MACjB,CAAC;IACL;;EAEJ+F,YAAY;IACRC,WAAWC;IACXC,QAAQC;;EAEZC,YAAY;IACRC,QAAAA;EACJ;AACJ;;;;;sBCnTIC,YAWQC,mBAAA;IAXCjI,UAAUkI,KAAQlI;EAAA,GAAA;uBACvB,WAAA;AAAA,aASY,CATZmI,YASYC,YATZC,WASY;QATA3I,MAAK;QAAa0D,SAAOkF,SAAOlF;QAAGoB,SAAO8D,SAAO9D;QAAGG,cAAa2D,SAAY3D;SAAUuD,KAAGK,IAAA,YAAA,CAAA,GAAA;2BAClG,WAAA;AAAA,iBAOK,CAPMC,MAAOtH,UAAlBuH,gBAAAC,UAAA,GAAAC,mBAOK,OAPLN,WAOK;;YAPgBO,KAAKN,SAAY3B;YAAckC,MAAK;YAAU,cAAYL,MAAOtH;YAAG4H,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAER,SAAchB,kBAAAgB,SAAAhB,eAAAyB,MAAAT,UAAAU,SAAA;YAAA;YAAG,SAAOd,KAAEe,GAAA,MAAA;aAAkBf,KAAIgB,KAAA,MAAA,CAAA,GAAA,CAChIhB,KAAAiB,OAAOpH,YAAnBqH,WAAgIlB,KAAAiB,QAAA,aAAA;;YAAhFE,eAAef,SAAItF;YAAGsG,iBAAkB,SAAlBA,gBAAkBvG,OAAK;AAAA,qBAAKuF,SAAA5C,gBAAgB3C,KAAK;YAAA;gBAEnH2F,UAAA,GAAAC,mBAEK,OAFLN,WAEK;;YAFC,SAAOH,KAAEe,GAAA,SAAA;YAAcH,SAAK,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAER,SAAcnF,kBAAAmF,SAAAnF,eAAA4F,MAAAT,UAAAU,SAAA;YAAA;YAAGO,aAAS,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAEjB,SAAcnF,kBAAAmF,SAAAnF,eAAA4F,MAAAT,UAAAU,SAAA;YAAA;YAAGQ,WAAO,OAAA,CAAA,MAAA,OAAA,CAAA,IAAA,WAAA;qBAAElB,SAAgB9C,oBAAA8C,SAAA9C,iBAAAuD,MAAAT,UAAAU,SAAA;;aAAUd,KAAGK,IAAA,SAAA,CAAA,GAAA,CACnHa,WAAYlB,KAAAiB,QAAA,SAAA,CAAA,GAAA,EAAA,EAAA,GAAA,IAAA,UAAA,IAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,IAAA,mBAAA,IAAA,IAAA,CAAA;;;;;;;;;;;ACJpC,IAAAM,UAAe;EACXC,MAAM;EACN,WAASC;EACTC,SAAO,SAAPA,WAAU;AACNC,YAAQC,KAAK,qDAAqD;EACtE;AACJ;", "names": ["BaseStyle", "extend", "name", "BaseFocusTrap", "BaseDirective", "extend", "style", "FocusTrapStyle", "FocusTrap", "mounted", "el", "binding", "_ref", "value", "disabled", "createHiddenFocusableElements", "bind", "autoElementFocus", "setAttribute", "$el", "updated", "_ref2", "unbind", "unmounted", "methods", "getComputedSelector", "selector", "concat", "_this", "_ref3", "onFocusIn", "onFocusOut", "$_pfocustrap_mutationobserver", "MutationObserver", "mutationList", "for<PERSON>ach", "mutation", "type", "contains", "document", "activeElement", "findNextFocusableElement", "_el", "focusableElement", "isFocusableElement", "$_pfocustrap_focusableselector", "getFirstFocusableElement", "isNotEmpty", "nextS<PERSON>ling", "focus", "disconnect", "observe", "childList", "$_pfocustrap_focusinlistener", "event", "$_pfocustrap_focusoutlistener", "addEventListener", "removeEventListener", "autoFocus", "options", "_objectSpread", "_ref4", "_ref4$autoFocusSelect", "autoFocusSelector", "_ref4$firstFocusableS", "firstFocusableSelector", "_ref4$autoFocus", "onFirstHiddenElementFocus", "_this$$el", "currentTarget", "relatedTarget", "$_pfocustrap_lasthiddenfocusableelement", "parentElement", "onLastHiddenElementFocus", "_this$$el2", "$_pfocustrap_firsthiddenfocusableelement", "getLastFocusableElement", "_this2", "_ref5", "_ref5$tabIndex", "tabIndex", "_ref5$firstFocusableS", "_ref5$lastFocusableSe", "lastFocusableSelector", "createFocusableElement", "onFocus", "createElement", "role", "firstFocusableElement", "lastFocusableElement", "prepend", "append", "classes", "root", "content", "BaseStyle", "extend", "name", "style", "name", "BaseComponent", "props", "dismissable", "type", "Boolean", "appendTo", "String", "Object", "baseZIndex", "Number", "autoZIndex", "breakpoints", "closeOnEscape", "style", "PopoverStyle", "provide", "$pcPopover", "$parentInstance", "script", "BasePopover", "inheritAttrs", "emits", "data", "visible", "watch", "immediate", "handler", "newValue", "bindOutsideClickListener", "unbindOutsideClickListener", "selfClick", "target", "eventTarget", "outsideClickListener", "<PERSON><PERSON><PERSON><PERSON>", "resizeListener", "container", "styleElement", "overlayEventListener", "documentKeydownListener", "beforeUnmount", "destroy", "destroyStyle", "unbindResizeListener", "ZIndex", "clear", "OverlayEventBus", "off", "mounted", "createStyle", "methods", "toggle", "event", "hide", "show", "currentTarget", "onContentClick", "onEnter", "el", "_this", "addStyle", "position", "top", "alignOverlay", "bindScrollListener", "bindResizeListener", "set", "$primevue", "config", "zIndex", "overlay", "e", "contains", "focus", "on", "$emit", "bindDocumentKeyDownListener", "onLeave", "unbindScrollListener", "unbindDocumentKeyDownListener", "onAfterLeave", "absolutePosition", "containerOffset", "getOffset", "targetOffset", "arrowLeft", "left", "setProperty", "$dt", "concat", "setAttribute", "isUnstyled", "addClass", "onContentKeydown", "code", "onButtonKeydown", "preventDefault", "focusTarget", "querySelector", "onKeyDown", "bind", "window", "document", "addEventListener", "removeEventListener", "_this2", "isClient", "isTargetClicked", "_this3", "ConnectedOverlayScrollHandler", "_this4", "isTouchDevice", "containerRef", "_this$$primevue", "createElement", "csp", "nonce", "head", "append<PERSON><PERSON><PERSON>", "innerHTML", "breakpoint", "$attrSelector", "<PERSON><PERSON><PERSON><PERSON>", "onOverlayClick", "emit", "originalEvent", "directives", "focustrap", "FocusTrap", "ripple", "<PERSON><PERSON><PERSON>", "components", "Portal", "_createBlock", "_component_Portal", "_ctx", "_createVNode", "_Transition", "_mergeProps", "$options", "ptm", "$data", "_withDirectives", "_openBlock", "_createElementBlock", "ref", "role", "onClick", "apply", "arguments", "cx", "ptmi", "$slots", "_renderSlot", "closeCallback", "keydownCallback", "onMousedown", "onKeydown", "script", "name", "Popover", "mounted", "console", "warn"]}