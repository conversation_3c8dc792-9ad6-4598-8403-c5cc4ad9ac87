# API Client Utility

This directory contains reusable functions for making HTTP requests to the backend API.

## Files

- `api.js` - Main API client with reusable functions
- `api-examples.js` - Usage examples for different scenarios

## Usage

### Basic API Calls

```javascript
import { apiGet, apiPost, apiPut, apiDelete } from '@/utils/api'

// GET request
const data = await apiGet('/users')

// POST request
const result = await apiPost('/users', { name: 'John', email: '<EMAIL>' })

// PUT request
const updated = await apiPut('/users/1', { name: '<PERSON>' })

// DELETE request
const deleted = await apiDelete('/users/1')
```

### Authentication

The API client automatically handles authentication tokens:

```javascript
import { loginApi, logoutApi, setAuthToken } from '@/utils/api'

// Login (automatically stores token)
const response = await loginApi('username', 'password')

// Manual token management
setAuthToken('your-jwt-token')

// Logout (clears token)
await logoutApi()
```

### Error Handling

```javascript
import { apiGet, ApiError } from '@/utils/api'

try {
  const data = await apiGet('/protected-endpoint')
} catch (error) {
  if (error instanceof ApiError) {
    console.error('API Error:', error.message, 'Status:', error.status)
    // Handle specific error cases
    if (error.status === 401) {
      // Redirect to login
    }
  } else {
    console.error('Network or other error:', error.message)
  }
}
```

### Configuration

Set the API base URL in your `.env` file:

```
VITE_API_BASE_URL=/api
```

For development with a separate backend server:

```
VITE_API_BASE_URL=http://localhost:3000/api
```

## Features

- **Automatic token management** - Stores and includes JWT tokens in requests
- **Error handling** - Custom ApiError class with status codes and messages
- **Cookie support** - Includes credentials for session-based authentication
- **Flexible configuration** - Environment-based API URL configuration
- **TypeScript-friendly** - JSDoc comments for better IDE support

## Backend Integration

The API client is designed to work with the backend login endpoint:

- **Endpoint**: `POST /api/login`
- **Request**: `{ _Username: string, _Password: string }`
- **Response**: `{ success: boolean, data: array, token: string, message: string }`

## Best Practices

1. **Always handle errors** - Use try-catch blocks and check for ApiError instances
2. **Use environment variables** - Configure API URLs through environment files
3. **Centralize API calls** - Create service functions for specific endpoints
4. **Token management** - Let the API client handle token storage and inclusion
5. **Loading states** - Show loading indicators during API calls
