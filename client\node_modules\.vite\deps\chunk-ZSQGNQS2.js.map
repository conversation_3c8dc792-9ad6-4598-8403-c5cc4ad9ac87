{"version": 3, "sources": ["../../src/usetoast/UseToast.js"], "sourcesContent": ["import { inject } from 'vue';\n\nexport const PrimeVueToastSymbol = Symbol();\n\nexport function useToast() {\n    const PrimeVueToast = inject(PrimeVueToastSymbol);\n\n    if (!PrimeVueToast) {\n        throw new Error('No PrimeVue Toast provided!');\n    }\n\n    return PrimeVueToast;\n}\n"], "mappings": ";;;;;AAEaA,IAAAA,sBAAsBC,OAAM;AAElC,SAASC,WAAW;AACvB,MAAMC,gBAAgBC,OAAOJ,mBAAmB;AAEhD,MAAI,CAACG,eAAe;AAChB,UAAM,IAAIE,MAAM,6BAA6B;EACjD;AAEA,SAAOF;AACX;", "names": ["PrimeVueToastSymbol", "Symbol", "useToast", "PrimeVueToast", "inject", "Error"]}