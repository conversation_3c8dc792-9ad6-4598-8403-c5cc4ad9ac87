/**
 * Example usage of hash utilities
 */

import { md5Hash, simpleHash, randomHash } from './hash.js'

// Example usage
console.log('MD5 Hash Examples:')
console.log('md5Hash("hello") =', md5Hash('hello'))
console.log('md5Hash("Hello World") =', md5Hash('Hello World'))
console.log('md5Hash("") =', md5Hash(''))

console.log('\nSimple Hash Examples:')
console.log('simpleHash("hello") =', simpleHash('hello'))
console.log('simpleHash("Hello World") =', simpleHash('Hello World'))

console.log('\nRandom Hash Examples:')
console.log('randomHash() =', randomHash())
console.log('randomHash(16) =', randomHash(16))
console.log('randomHash(64) =', randomHash(64))

// Example: Using MD5 for creating unique identifiers
function createUniqueId(data) {
  const timestamp = Date.now().toString()
  const randomPart = Math.random().toString()
  const combined = `${data}-${timestamp}-${randomPart}`
  return md5Hash(combined)
}

console.log('\nUnique ID Example:')
console.log('createUniqueId("user123") =', createUniqueId('user123'))
console.log('createUniqueId("user123") =', createUniqueId('user123')) // Different each time

// Example: Using MD5 for data integrity checking
function createDataFingerprint(obj) {
  const jsonString = JSON.stringify(obj, Object.keys(obj).sort())
  return md5Hash(jsonString)
}

const userData = { name: 'John', age: 30, email: '<EMAIL>' }
console.log('\nData Fingerprint Example:')
console.log('createDataFingerprint(userData) =', createDataFingerprint(userData))
